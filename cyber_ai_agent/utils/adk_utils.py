from typing import Any

from google.adk.agents import BaseAgent, LlmAgent
from google.adk.agents.invocation_context import Invocation<PERSON>ontext
from google.adk.models.lite_llm import LiteLlm
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from loguru import logger
from pyrit.prompt_target import OpenAIChatTarget

from cyber_ai_agent.config import PyritLlmCfg, SessionCfg


def build_agent(
    cfg: dict,
    **kwargs: Any,
) -> LlmAgent:
    """Helper to create a generic LLM agent."""

    llm = LiteLlm(**cfg)
    return LlmAgent(
        model=llm,
        **kwargs,
    )


def build_pyrit_agent(cfg: PyritLlmCfg) -> OpenAIChatTarget:
    if cfg.model_name.startswith("azure/"):
        cfg.model_name = cfg.model_name[len("azure/"):]

    return OpenAIChatTarget(**cfg.model_dump())


async def invoke_llm(
    llm: LlmAgent,
    query: str,
    ctx: InvocationContext,
) -> str:
    ctx.session.state.setdefault("llm_calls", 0)
    ctx.session.state["query"] = query
    ctx.session.state["llm_calls"] += 1
    final_response: str = ""

    async for event in llm.run_async(ctx):
        if event.is_final_response():
            final_response = event.content.parts[0].text

    return final_response


async def invoke_llm_runner(
    llm: LlmAgent,
    query: str,
    ctx: InvocationContext,
) -> str:
    ctx.session.state.setdefault("llm_calls", 0)
    ctx.session.state["query"] = query
    ctx.session.state["llm_calls"] += 1
    final_response: str = ""

    runner = Runner(
        agent=llm,
        app_name=ctx.app_name,
        session_service=ctx.session_service
    )

    query_message = types.Content(role='user', parts=[types.Part(text=query)])

    async for event in runner.run_async(
        user_id=ctx.user_id,
        session_id=ctx.session_id,
        new_message=query_message,
    ):
        if event.is_final_response():
            if event.content and event.content.parts:
                final_response = event.content.parts[0].text

    return final_response

async def create_and_run_session(
    agent: BaseAgent,
    session_service: InMemorySessionService,
    session_cfg: SessionCfg,
    state: dict,
) -> dict | None:
    logger.info(f"═══ Running {agent.name} | Session ID: {session_cfg.session_id} ═══")

    await session_service.create_session(
        app_name=session_cfg.app_name,
        user_id=session_cfg.user_id,
        session_id=session_cfg.session_id,
        state=state,
    )

    runner = Runner(
        agent=agent,
        app_name=session_cfg.app_name,
        session_service=session_service
    )

    async for event in runner.run_async(
        user_id=session_cfg.user_id,
        session_id=session_cfg.session_id,
        new_message=None,
    ):
        if event.content and event.content.parts:
            logger.info(event.content)

    final_session = await session_service.get_session(
        app_name=session_cfg.app_name,
        user_id=session_cfg.user_id,
        session_id=session_cfg.session_id,
    )

    if final_session and hasattr(final_session, "state") and final_session.state:
        return final_session.state

    return None
