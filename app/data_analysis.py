import os

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from pymongo.cursor import Cursor
from pymongo.synchronous.collection import Collection
from pymongo.synchronous.database import Database
from result_page import render_card

from cyber_ai_agent.mongo_utils import close_mongo_client, get_database

MONGODB_APP_DB_NAME = os.getenv("MONGODB_APP_DB_NAME", "cyber_ai_agent_db")


def show_data_from_db() -> None:
    # Get database instance
    db: Database = get_database(MONGODB_APP_DB_NAME)
    collection_name = "attack_results"

    # Get collection
    collection: Collection = db[collection_name]

    # Fetch all documents
    cursor: Cursor = collection.find()
    docs: list = list(cursor)
    if not docs:
        st.info("No data found in 'attack_results' collection.")
        return

    # Convert to DataFrame
    df = pd.DataFrame(docs)

    # First filter by model for all analysis
    selected_models = st.multiselect(
        "Select target model(s) to analyze:",
        options=df["target_model"].unique(),
        default=df["target_model"].unique(),
    )
    df = df[df["target_model"].isin(selected_models)]

    df["success"] = df["success"].fillna(False).astype(bool)

    # Grouping at attack_id level, attack successful if one iteration successful
    df_attack = (
        df.groupby(["attacker_kind", "attack_id"])["success"]
        .agg(success_attack="max", iterations="count")
        .reset_index()
    )

    #==============================================================================================
    # Bar chart: success rate by attacker_kind

    # Success rate calculation by attacker_kind
    st.subheader("Vulnerability to different attack strategy")
    df_grouped_by_attacker = (
        df_attack.groupby("attacker_kind")["success_attack"]
        .agg(["sum", "count"])  # sum = nb d'attaques réussies, count = nb total d'attaques
        .rename(columns={"sum": "successful_attacks", "count": "total_attacks"})
    )
    df_success_rate_by_attacker = df_grouped_by_attacker.assign(
        success_rate = df_grouped_by_attacker["successful_attacks"] / df_grouped_by_attacker["total_attacks"] * 100
    )
    # Sort by success_rate
    df_success_rate_by_attacker = df_success_rate_by_attacker.reset_index().sort_values(by="success_rate", ascending=False)

    max_val = df_success_rate_by_attacker["success_rate"].max()

    fig = px.bar(
        df_success_rate_by_attacker,
        x="attacker_kind",
        y="success_rate",
        text=df_success_rate_by_attacker["success_rate"].round(1).astype(str) + "%",
        labels={"attacker_kind": "Attacker Kind", "success_rate": "Success Rate (%)"},
        color="attacker_kind",
    )
    fig.update_traces(textposition="outside")
    fig.update_layout(
        xaxis_tickangle=0,  # <= Légendes horizontales
        showlegend=False,
        yaxis_title="Success rate (%)",
        yaxis=dict(tickformat=".0f", range=[0, max_val * 1.2]),
    )
    st.plotly_chart(fig, use_container_width=True)

    #==============================================================================================
    # Web diagramm of vulnerabilities
    categories = df_success_rate_by_attacker["attacker_kind"].tolist()
    values = df_success_rate_by_attacker["success_rate"].tolist()
    values += values[:1]  # fermer le radar

    categories += categories[:1]  # fermer le radar

    # Créer la figure Radar avec Plotly
    fig = go.Figure(
        data=[
            go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name='Success Rate (%)'
            )
        ],
        layout=go.Layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 80]
                )
            ),
            showlegend=False
        )
    )

    # Afficher dans Streamlit
    st.plotly_chart(fig)

    #==============================================================================================
    # Success rate by iteration for LLM attackers
    st.subheader("Cumulative Success Rate by Iteration")
    filtered_kinds = ["crescendo", "crescendo_custom", "pair", "renellm"]
    df_success_rate_iteration = df_attack[df_attack["attacker_kind"].isin(filtered_kinds)]
    df_success_rate_iteration = (
        df_success_rate_iteration.groupby(["attacker_kind", "iterations"])["success_attack"]
        .agg(successful_attacks_at_this_iteration="sum")
        .reset_index()
    )

    df_success_rate_iteration = df_success_rate_iteration.merge(df_grouped_by_attacker, on="attacker_kind")
    df_success_rate_iteration = df_success_rate_iteration.sort_values(["attacker_kind", "iterations"])
    df_success_rate_iteration["cumulative_success"] = (
        df_success_rate_iteration.groupby("attacker_kind")["successful_attacks_at_this_iteration"].cumsum()
    )
    df_success_rate_iteration["cumulative_success_rate"] = (
        df_success_rate_iteration["cumulative_success"] / df_success_rate_iteration["total_attacks"] * 100
    )
    fig = px.line(
        df_success_rate_iteration,
        x="iterations",
        y="cumulative_success_rate",
        color="attacker_kind",
        markers=True,
        labels={
            "iterations": "Number of Iterations",
            "cumulative_success_rate": "Cumulative Success Rate (%)",
            "attacker_kind": "Attacker"
        }
    )
    fig.update_layout(yaxis=dict(tickformat=".1f"))
    st.plotly_chart(fig, use_container_width=True)

    #==============================================================================================
    # Filter by attacker_type if column exists
    st.subheader("Attack details")
    attacker_types = sorted( ["All"] + df["attacker_kind"].dropna().unique().tolist())
    selected_type = st.selectbox("Filter by attacker_kind", attacker_types)
    if selected_type != "All":
        df_filtered = df[df["attacker_kind"] == selected_type].copy()
        df_attack_filtered = df_attack[df_attack["attacker_kind"] == selected_type].copy()
    else :
        df_filtered = df
        df_attack_filtered = df_attack

    df_attack_filtered_success = df_attack_filtered[df_attack_filtered["success_attack"]].copy()

    st.markdown(
        f"""
        <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
            {render_card("Number of attacks logged in DB", str(len(df_attack_filtered)), "2.2em")}
            {render_card("Number of successful attacks", str(len(df_attack_filtered_success)), "2.2em", "#27ae60")}
            {render_card("Success rate (%)", f"{(len(df_attack_filtered_success) / len(df_attack_filtered) * 100):.1f}%" if len(df) > 0 else "N/A", "2.2em", "#800080")}
        </div>
        """,
        unsafe_allow_html=True,
    )

    if "_id" in df.columns:
        df["_id"] = df["_id"].astype(str)
    # Show only relevant columns if present
    # columns_to_show = [col for col in ["_id", "prompte", "heure", "reponse"] if col in df.columns]
    st.dataframe(df_filtered, hide_index=True)
    close_mongo_client()
