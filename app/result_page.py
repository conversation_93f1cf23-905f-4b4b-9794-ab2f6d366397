# result_page.py

import textwrap

import pandas as pd
import streamlit as st

from cyber_ai_agent.models.schemas import AttackResult


def render_card(
    title: str,
    value: str,
    value_font_size: str = "1.5em",
    value_color: str = "#34495e",
    background_color: str="#f4f6fa",
) -> str:
    return textwrap.dedent(f"""
        <div style='background: {background_color}; border-radius: 16px; padding: 1.2em 2em; min-width: 180px; box-shadow: 0 2px 8px #0001;'>
            <div style='font-size: 1.1em; color: #555;'>{title}</div>
            <div style='font-size: {value_font_size}; font-weight: bold; color: {value_color};'>{value}</div>
        </div>
    """).strip()


def show_attack_results(attack_logs_dicts: list[dict | None]) -> None:
    attack_logs = [AttackResult.model_validate(log) for log in attack_logs_dicts]
    if attack_logs:
        data = []
        for log in attack_logs:
            data.append(
                {
                    "Attack ID": log.attack_id,
                    "Iteration": log.iteration,
                    "Objective": log.objective.name,
                    "Prompt": log.objective.prompt_reference,
                    "Template": log.template,
                    "Converter": log.converter,
                    "Full Prompt": log.prompt,
                    "Response": log.response,
                    "Attack Type": log.attacker_kind,
                    "Score": log.score,
                    "Success": log.success,
                }
            )
        df = pd.DataFrame(data)

        attacker_types = sorted(df["Attack Type"].dropna().unique().tolist())

        ##############################################################################
        st.markdown("### Attack Summary")
        summary_cards = []
        attacker_type_labels = {
            "crescendo": "LLM Crescendo Attacks",
            "crescendo_custom": "LLM Crescendo Custom Attacks",
            "pair": "LLM Pair Attacks",
            "renellm": "LLM ReNe Attacks",
            "template": "Template Attacks",
            "encryption": "Encryption Attacks",
            "qroa_suffix": "Suffix QROA Attacks"
        }
        for attacker_type in attacker_types:
            is_template_attack_successful: bool = (
                (df["Attack Type"] == attacker_type) & (df["Success"])
            ).any()
            label = attacker_type_labels.get(attacker_type, attacker_type)
            summary_cards.append(
                render_card(
                    f"Robust against {label}",
                    "No" if is_template_attack_successful else "Yes",
                    "2.2em",
                    value_color="#e74c3c" if is_template_attack_successful else "#27ae60",
                    background_color="#faeaea" if is_template_attack_successful else "#eafaf1",
                )
            )

        st.markdown(
            f"""
                <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
                        {"".join(summary_cards)}
                </div>
                """,
            unsafe_allow_html=True,
        )

        ##############################################################################
        st.markdown("### Attack Information")
        attack_types = [
            ("crescendo", "LLM Crescendo iterations"),
            ("crescendo_custom", "LLM Crescendo Custom iterations"),
            ("pair", "LLM Pair iterations"),
            ("renellm", "LLM ReNe iterations"),
            ("template", "Template attacks"),
            ("encryption", "Encryption attacks"),
            ("qroa_suffix", "Suffix QROA attacks")
        ]

        cards = []

        for attack_key, title in attack_types:
            if attack_key in attacker_types:
                count = (df["Attack Type"] == attack_key).sum()
                cards.append(render_card(title, str(count), "2.2em"))

        st.markdown(
            f"""
                <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
                        {"".join(cards)}
                </div>
                """,
            unsafe_allow_html=True,
        )

        ##############################################################################
        st.markdown("### Attack details")

        selected_type = st.selectbox("Filter by attacker_kind", ["All"] + attacker_types)
        if selected_type != "All":
            df = df[df["Attack Type"] == selected_type].copy()

        st.dataframe(df, hide_index=True)
        if selected_type == "template":
            df_grouped = (
                df.groupby(["Prompt", "Template"])
                .agg(
                    N_iterations=("Iteration", "count"),
                    Mean_score=("Score", "mean"),
                )
                .reset_index()
                .sort_values(by="Mean_score", ascending=False)
                .assign(Mean_score=lambda df: df["Mean_score"].round(2))
            )
            st.write("**Summary of attacks by prompt and template:**")
            st.dataframe(df_grouped, hide_index=True)
    else:
        st.warning("No attack logs available. Launch an attack in the previous section.")
